import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

/**
 * 调试service参数的脚本
 * 模拟controller传递给service的确切参数
 */

async function debugServiceParams() {
    console.log('🔍 开始调试service参数...\n');

    try {
        // 模拟controller处理后的参数（基于错误日志中的请求体）
        const requestBody = {
            "name": "aaa",
            "gender": "secret",
            "phone": "13145211314",
            "idCard": "360111201405022070",
            "birthday": "1559318400000",
            "source": "朋友推荐",
            "sourceDesc": "小白的弟弟",
            "referrer": "小白",
            "intention": "A",
            "follower": "org-admin-id",
            "address": "aaaaaa",
            "remarks": "",
            "type": "formal"
        };

        // 模拟request.user（从认证中间件获取）
        const requestUser = {
            id: "org-admin-id",
            institutionId: "3a2be69a-9f60-4ff8-9dea-60ea0c55249a"
        };

        console.log('📋 原始请求体:');
        console.log(JSON.stringify(requestBody, null, 2));

        console.log('\n👤 认证用户信息:');
        console.log(JSON.stringify(requestUser, null, 2));

        // 模拟controller中的参数处理（studentController.js 第732-748行）
        const serviceParams = {
            name: requestBody.name,
            gender: requestBody.gender,
            phone: requestBody.phone,
            birthday: Number(requestBody.birthday), // 这里是关键！
            sourceDesc: requestBody.sourceDesc,
            intention: requestBody.intention,
            follower: requestBody.follower,
            source: requestBody.source,
            referrer: requestBody.referrer,
            address: requestBody.address,
            idCard: requestBody.idCard,
            remarks: requestBody.remarks,
            type: requestBody.type,
            institutionId: requestUser.institutionId,
            operatorId: requestUser.id
        };

        console.log('\n📋 传递给service的参数:');
        console.log(JSON.stringify(serviceParams, null, 2));

        // 检查birthday字段的类型
        console.log('\n🔍 Birthday字段分析:');
        console.log(`   原始值: "${requestBody.birthday}" (类型: ${typeof requestBody.birthday})`);
        console.log(`   Number转换后: ${serviceParams.birthday} (类型: ${typeof serviceParams.birthday})`);

        // 模拟service中的数据准备（studentService.js 第1917-1936行）
        const studentData = {
            id: uuidv4(),
            name: serviceParams.name,
            phone: serviceParams.phone,
            gender: serviceParams.gender,
            birthday: serviceParams.birthday, // 这里没有BigInt转换！
            sourceDesc: serviceParams.sourceDesc,
            intentLevel: serviceParams.intention,
            followerId: serviceParams.follower ? serviceParams.follower : null,
            source: serviceParams.source,
            referrer: serviceParams.referrer,
            address: serviceParams.address,
            idCard: serviceParams.idCard,
            remarks: serviceParams.remarks,
            institutionId: serviceParams.institutionId,
            type: serviceParams.type,
            operatorId: serviceParams.operatorId
        };

        console.log('\n📋 准备创建的学生数据:');
        console.log(JSON.stringify(studentData, null, 2));

        console.log('\n⚠️  问题发现:');
        console.log(`   birthday字段类型: ${typeof studentData.birthday}`);
        console.log('   数据库期望类型: BigInt');
        console.log('   这可能导致类型不匹配的外键约束问题！');

        // 尝试修正birthday字段
        const correctedStudentData = {
            ...studentData,
            birthday: studentData.birthday ? BigInt(studentData.birthday) : null
        };

        console.log('\n🔧 修正后的数据:');
        console.log(JSON.stringify(correctedStudentData, (key, value) => 
            typeof value === 'bigint' ? value.toString() : value, 2));

        // 测试修正后的数据
        console.log('\n🧪 测试修正后的数据创建...');
        
        const result = await prisma.student.create({
            data: correctedStudentData,
            include: {
                institution: { select: { name: true } },
                operator: { select: { name: true } },
                follower: { select: { name: true } }
            }
        });

        console.log('✅ 修正后的数据创建成功！');
        console.log(`   学生ID: ${result.id}`);
        console.log(`   姓名: ${result.name}`);
        console.log(`   生日: ${result.birthday} (类型: ${typeof result.birthday})`);

        // 清理测试数据
        await prisma.student.delete({
            where: { id: result.id }
        });

        console.log('\n💡 解决方案:');
        console.log('   需要在studentService.js中修正birthday字段的类型转换');
        console.log('   将 birthday: serviceParams.birthday 改为 birthday: serviceParams.birthday ? BigInt(serviceParams.birthday) : null');

        console.log('\n🎉 调试完成！');

    } catch (error) {
        console.error('❌ 调试过程中出现错误:', error);
        
        if (error.code === 'P2003') {
            console.log('\n💡 这确实是外键约束错误');
            console.log('   但可能是由于数据类型不匹配导致的');
        }
        
        throw error;
    }
}

// 执行调试
debugServiceParams().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
});

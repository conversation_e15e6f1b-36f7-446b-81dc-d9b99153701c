/**
 * 测试学生创建API的脚本
 * 模拟完整的API调用流程
 */

const API_BASE = 'http://localhost:3000';

// 检查服务器是否运行
async function checkServer() {
    try {
        const response = await fetch(`${API_BASE}/health`);
        return response.ok;
    } catch (error) {
        return false;
    }
}

// 登录获取token
async function login() {
    try {
        console.log('🔐 正在登录...');
        
        const response = await fetch(`${API_BASE}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                account: 'orgadmin',
                password: '123456'
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('✅ 登录成功');
            console.log(`   用户: ${data.user?.name || 'N/A'}`);
            console.log(`   Token: ${data.accessToken ? '已获取' : '未获取'}`);
            return {
                success: true,
                token: data.accessToken,
                user: data.user
            };
        } else {
            const error = await response.text();
            console.log(`❌ 登录失败: ${response.status} - ${error}`);
            return { success: false, error };
        }
    } catch (error) {
        console.log(`❌ 登录异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 测试创建学生
async function testCreateStudent(token) {
    try {
        console.log('\n👨‍🎓 正在测试创建学生...');
        
        const studentData = {
            "name": "测试学生",
            "gender": "male",
            "phone": "13800138000",
            "idCard": "360111201405022070",
            "birthday": "1559318400000",
            "source": "朋友推荐",
            "sourceDesc": "测试描述",
            "referrer": "推荐人",
            "intention": "A",
            "follower": "org-admin-id",
            "address": "测试地址",
            "remarks": "测试备注",
            "type": "formal"
        };

        console.log('📋 请求数据:');
        console.log(JSON.stringify(studentData, null, 2));

        const response = await fetch(`${API_BASE}/api/students/add`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(studentData)
        });

        console.log(`\n📡 响应状态: ${response.status}`);

        if (response.ok) {
            const result = await response.json();
            console.log('✅ 学生创建成功');
            console.log('📋 响应数据:');
            console.log(JSON.stringify(result, null, 2));
            return { success: true, data: result };
        } else {
            const error = await response.text();
            console.log(`❌ 学生创建失败: ${response.status}`);
            console.log('📋 错误详情:');
            console.log(error);
            return { success: false, error, status: response.status };
        }
    } catch (error) {
        console.log(`❌ 请求异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 获取用户信息
async function getUserInfo(token) {
    try {
        console.log('\n👤 获取用户信息...');
        
        const response = await fetch(`${API_BASE}/api/users/profile`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('✅ 用户信息获取成功');
            console.log('📋 用户数据:');
            console.log(JSON.stringify(data, null, 2));
            return { success: true, data };
        } else {
            const error = await response.text();
            console.log(`❌ 获取用户信息失败: ${response.status} - ${error}`);
            return { success: false, error };
        }
    } catch (error) {
        console.log(`❌ 获取用户信息异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 主测试函数
async function runTest() {
    console.log('🧪 开始学生创建API测试...\n');

    // 检查服务器状态
    const serverRunning = await checkServer();
    if (!serverRunning) {
        console.log('❌ 服务器未运行，请先启动服务器: npm run dev');
        return;
    }
    console.log('✅ 服务器运行正常\n');

    // 登录
    const loginResult = await login();
    if (!loginResult.success) {
        console.log('❌ 登录失败，无法继续测试');
        return;
    }

    // 获取用户信息
    await getUserInfo(loginResult.token);

    // 测试创建学生
    const createResult = await testCreateStudent(loginResult.token);
    
    if (createResult.success) {
        console.log('\n🎉 学生创建测试成功！');
    } else {
        console.log('\n💡 调试建议:');
        console.log('   1. 检查数据库外键约束');
        console.log('   2. 检查用户机构关联');
        console.log('   3. 检查认证中间件设置的request.user');
        console.log('   4. 运行: node scripts/check-foreign-keys.js');
    }
}

// 执行测试
runTest().catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
});

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 验证角色权限分配脚本
 * 检查各个角色的权限分配是否正确
 */

async function verifyRolePermissions() {
    console.log('🔍 开始验证角色权限分配...\n');

    try {
        // 获取所有角色及其权限
        const roles = await prisma.role.findMany({
            include: {
                rolePermissions: {
                    include: {
                        permission: true
                    }
                }
            }
        });

        for (const role of roles) {
            console.log(`📋 角色: ${role.name} (${role.code})`);
            console.log(`   描述: ${role.description || '无描述'}`);
            console.log(`   权限数量: ${role.rolePermissions.length}`);
            
            if (role.rolePermissions.length > 0) {
                console.log('   权限列表:');
                
                // 按权限类型分组显示
                const permissionsByCategory = {};
                
                role.rolePermissions.forEach(rp => {
                    const permission = rp.permission;
                    const category = permission.code.split(':')[0];
                    
                    if (!permissionsByCategory[category]) {
                        permissionsByCategory[category] = [];
                    }
                    
                    permissionsByCategory[category].push({
                        code: permission.code,
                        name: permission.name
                    });
                });

                // 显示分类权限
                Object.keys(permissionsByCategory).sort().forEach(category => {
                    console.log(`     ${category.toUpperCase()}:`);
                    permissionsByCategory[category].forEach(perm => {
                        console.log(`       - ${perm.code} (${perm.name})`);
                    });
                });
            } else {
                console.log('   ⚠️  该角色没有分配任何权限');
            }
            
            console.log(''); // 空行分隔
        }

        // 验证用户角色分配
        console.log('👥 用户角色分配验证:\n');
        
        const users = await prisma.user.findMany({
            include: {
                userRoles: {
                    include: {
                        role: true
                    }
                }
            }
        });

        users.forEach(user => {
            console.log(`👤 用户: ${user.name} (${user.account})`);
            if (user.userRoles.length > 0) {
                user.userRoles.forEach(ur => {
                    console.log(`   角色: ${ur.role.name} (${ur.role.code})`);
                });
            } else {
                console.log('   ⚠️  该用户没有分配任何角色');
            }
            console.log('');
        });

        // 统计信息
        console.log('📊 统计信息:');
        const totalPermissions = await prisma.permission.count();
        const totalRoles = await prisma.role.count();
        const totalUsers = await prisma.user.count();
        const totalRolePermissions = await prisma.rolePermission.count();
        const totalUserRoles = await prisma.userRole.count();

        console.log(`   总权限数: ${totalPermissions}`);
        console.log(`   总角色数: ${totalRoles}`);
        console.log(`   总用户数: ${totalUsers}`);
        console.log(`   角色权限关联数: ${totalRolePermissions}`);
        console.log(`   用户角色关联数: ${totalUserRoles}`);

        console.log('\n✅ 角色权限验证完成！');

    } catch (error) {
        console.error('❌ 验证过程中出现错误:', error);
        throw error;
    }
}

// 执行验证
verifyRolePermissions().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
});

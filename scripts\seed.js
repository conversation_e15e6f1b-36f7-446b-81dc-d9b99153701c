import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

/**
 * 统一的数据库种子脚本
 * 包含权限、角色、用户、机构、菜单等所有初始数据的创建
 */
/**
 * 清理现有数据
 */
async function cleanupExistingData() {
    await prisma.rolePermission.deleteMany();
    await prisma.userRole.deleteMany();
    await prisma.userInstitution.deleteMany();
    await prisma.role.deleteMany();
    await prisma.institution.deleteMany();
    await prisma.user.deleteMany();
    await prisma.menu.deleteMany();
    await prisma.permission.deleteMany();
}

/**
 * 创建所有权限
 */
async function createPermissions() {
    console.log('📋 创建权限...');

    // 教务中心权限
    await prisma.permission.createMany({
        data: [
            // 教务管理中心
            { code: 'academic:access', name: '访问教务中心' },
            { code: 'student:create', name: '创建学员' },
            { code: 'student:delete', name: '删除学员' },
            { code: 'student:update', name: '更新学员' },
            { code: 'student:read', name: '查看学员' },
            { code: 'student', name: '学员管理' },
            { code: 'student:read:all', name: '查看所有学员' },
            { code: 'student:read:active', name: '查看在读学员' },
            { code: 'student:package:read', name: '查询学员套餐' },
            { code: 'student:attendance:read', name: '查询考勤记录' },
            { code: 'student:product:adjustment:read', name: '查询产品调整记录' },
            { code: 'student:read:graduated', name: '查询毕业学员' },
            { code: 'student:class:read', name: '上课查询' },
            // 班级管理
            { code: 'class:create', name: '创建班级' },
            { code: 'class:delete', name: '删除班级' },
            { code: 'class:update', name: '更新班级' },
            { code: 'class:read', name: '查看班级' },
            // 课表管理
            { code: 'schedule:create', name: '创建课表' },
            { code: 'schedule:delete', name: '删除课表' },
            { code: 'schedule:update', name: '更新课表' },
            { code: 'schedule:read', name: '查看课表' },
            // 课程管理
            { code: 'course:create', name: '创建课程' },
            { code: 'course:delete', name: '删除课程' },
            { code: 'course:update', name: '更新课程' },
            { code: 'course:read', name: '查看课程' },
            // 套餐管理
            { code: 'product:create', name: '创建套餐' },
            { code: 'product:delete', name: '删除套餐' },
            { code: 'product:update', name: '更新套餐' },
            { code: 'product:read', name: '查看套餐' },
        ]
    });

    // 招生中心权限
    await prisma.permission.createMany({
        data: [
            { code: 'enrollment:access', name: '访问招生中心' },
            { code: 'followUp:read', name: '跟进管理' },
            { code: 'followUp:target:read', name: '意向学员' },
            { code: 'followUp:target:follow:read', name: '跟进记录' },
            { code: 'followup:highseas:read', name: '公海池' },
        ]
    });

    // 财务中心权限
    await prisma.permission.createMany({
        data: [
            { code: 'finance:access', name: '访问财务中心' },
            { code: 'finance:sales:read', name: '销售记录' },
            { code: 'finance:purchase:read', name: '购买记录' },
            { code: 'finance:refund:read', name: '退款记录' },
            { code: 'finance:bill:read', name: '账单列表' },
            { code: 'finance:bill:create', name: '创建账单' },
            { code: 'finance:bill:stats:read', name: '账单统计' },
            { code: 'finance:salary:read', name: '工资列表' },
            { code: 'finance:teaching:stats:read', name: '授课统计' },
            { code: 'finance:salary:create', name: '创建工资单' },
        ]
    });

    // 数据中心权限
    await prisma.permission.createMany({
        data: [
            { code: 'data:access', name: '访问数据中心' },
            { code: 'data:academic:read', name: '教务数据' },
            { code: 'data:academic:overview:read', name: '整体概览' },
            { code: 'data:academic:consumption:read', name: '消课汇总' },
            { code: 'data:academic:fees:read', name: '学员费用' },
            { code: 'data:sales:read', name: '销售数据' },
            { code: 'data:sales:overview:read', name: '整体概览' },
            { code: 'data:sales:staff:read', name: '销售员统计' },
            { code: 'data:sales:student:read', name: '学员概览' },
        ]
    });

    // 机构中心权限
    await prisma.permission.createMany({
        data: [
            { code: 'org:access', name: '访问机构中心' },
            { code: 'org:info:read', name: '机构信息' },
            { code: 'org:info:update', name: '更新机构' },
            { code: 'org:address:create', name: '创建地址' },
            { code: 'org:staff:read', name: '员工列表' },
            { code: 'org:staff:create', name: '创建员工' },
            { code: 'org:staff:update', name: '更新员工' },
            { code: 'org:staff:delete', name: '删除员工' },
            { code: 'org:position:read', name: '岗位列表' },
            { code: 'org:position:create', name: '创建岗位' },
            { code: 'org:position:update', name: '更新岗位' },
            { code: 'org:position:delete', name: '删除岗位' },
            { code: 'org:position:permission:read', name: '岗位权限' },
            { code: 'org:attendance:read', name: '考勤记录' },
            { code: 'org:attendance:stats:read', name: '考勤统计' },
            { code: 'org:classroom:read', name: '教室列表' },
            { code: 'org:classroom:create', name: '创建教室' },
            { code: 'org:classroom:update', name: '更新教室' },
            { code: 'org:classroom:delete', name: '删除教室' },
            { code: 'org:log:read', name: '操作日志' },
        ]
    });

    // 通用权限
    await prisma.permission.createMany({
        data: [
            { code: 'dashboard:access', name: '访问仪表盘' },
            { code: 'notification:access', name: '访问通知管理' },
            // { code: 'aitools:access', name: '访问AI工具' },
            // { code: 'system:access', name: '访问系统管理' },
            // { code: 'system:user:view', name: '查看用户' },
            // { code: 'system:user:create', name: '创建用户' },
            // { code: 'system:user:update', name: '更新用户' },
            // { code: 'system:user:delete', name: '删除用户' },
            // { code: 'system:role:view', name: '查看角色' },
            // { code: 'system:role:create', name: '创建角色' },
            // { code: 'system:role:update', name: '更新角色' },
            // { code: 'system:role:delete', name: '删除角色' },
            // { code: 'system:menu:view', name: '查看菜单' },
            // { code: 'system:menu:create', name: '创建菜单' },
            // { code: 'system:menu:update', name: '更新菜单' },
            // { code: 'system:menu:delete', name: '删除菜单' },
            // { code: 'system:institution:view', name: '查看机构' },
            // { code: 'system:institution:create', name: '创建机构' },
            // { code: 'system:institution:update', name: '更新机构' },
            // { code: 'system:institution:delete', name: '删除机构' },
        ]
    });
}

/**
 * 创建角色
 */
async function createRoles() {
    console.log('👥 创建角色...');

    // 创建超级管理员角色
    const superAdminRole = await prisma.role.upsert({
        where: { code: 'superadmin' },
        update: {},
        create: {
            name: '超级管理员',
            code: 'superadmin',
            description: '拥有所有权限的超级管理员',
        }
    });

    // 创建教师角色
    const teacherRole = await prisma.role.upsert({
        where: { code: 'teacher' },
        update: {},
        create: {
            id: 'b7e46980-6adc-4af3-8553-d419f110cc44',
            code: 'teacher',
            name: '教师',
            description: '负责教学的教师角色',
        }
    });

    // 创建机构管理员角色
    const orgAdminRole = await prisma.role.upsert({
        where: { code: 'orgadmin' },
        update: {},
        create: {
            code: 'orgadmin',
            name: '机构管理员',
            description: '负责机构管理的管理员角色',
        }
    });

    return { superAdminRole, teacherRole, orgAdminRole };
}

/**
 * 分配角色权限
 */
async function assignRolePermissions(roles) {
    console.log('🔐 分配角色权限...');

    const { superAdminRole, teacherRole, orgAdminRole } = roles;

    // 定义角色权限映射
    const rolePermissionMappings = {
        teacher: [
            'dashboard:access',
            'academic:access',
            'student:read',
            'class:read',
            'schedule:read',
            'course:read',
        ],
        orgadmin: [
            'dashboard:access',
            // 机构中心权限
            'org:access',
            'org:info:read',
            'org:info:update',
            'org:staff:read',
            'org:staff:create',
            'org:staff:update',
            'org:staff:delete',
            'org:position:read',
            'org:position:create',
            'org:position:update',
            'org:position:delete',
            'org:position:permission:read',
            'org:attendance:read',
            'org:attendance:stats:read',
            'org:classroom:read',
            'org:classroom:create',
            'org:classroom:update',
            'org:classroom:delete',
            'org:log:read',
            // 系统管理权限
            'system:access',
            'system:user:view',
            'system:user:create',
            'system:user:update',
            'system:user:delete',
            'system:role:view',
            'system:role:create',
            'system:role:update',
            'system:role:delete',
            'system:menu:view',
            'system:menu:create',
            'system:menu:update',
            'system:menu:delete',
            'system:institution:view',
            'system:institution:create',
            'system:institution:update',
            'system:institution:delete',
            // 通知管理权限
            'notification:access',
            // 教务中心基础权限（机构管理员应该能查看教务数据）
            'academic:access',
            'student:read',
            'class:read',
            'schedule:read',
            'course:read',
            'package:read',
            // 财务中心基础权限（机构管理员应该能查看财务数据）
            'finance:access',
            'finance:sales:read',
            'finance:bill:read',
            'finance:salary:read',
            // 数据中心权限（机构管理员应该能查看数据统计）
            'data:access',
            'data:academic:read',
            'data:sales:read',
        ]
    };

    // 清理现有角色权限关联
    await prisma.rolePermission.deleteMany({
        where: {
            roleId: {
                in: [superAdminRole.id, teacherRole.id, orgAdminRole.id]
            }
        }
    });

    // 分配权限给教师角色
    for (const permissionCode of rolePermissionMappings.teacher) {
        const permission = await prisma.permission.findUnique({
            where: { code: permissionCode }
        });

        if (permission) {
            await prisma.rolePermission.create({
                data: {
                    roleId: teacherRole.id,
                    permissionId: permission.id
                }
            });
        } else {
            console.warn(`权限不存在: ${permissionCode}`);
        }
    }

    // 分配权限给机构管理员角色
    for (const permissionCode of rolePermissionMappings.orgadmin) {
        const permission = await prisma.permission.findUnique({
            where: { code: permissionCode }
        });

        if (permission) {
            await prisma.rolePermission.create({
                data: {
                    roleId: orgAdminRole.id,
                    permissionId: permission.id
                }
            });
        } else {
            console.warn(`权限不存在: ${permissionCode}`);
        }
    }

    // 超级管理员拥有所有权限
    const allPermissions = await prisma.permission.findMany();
    for (const permission of allPermissions) {
        await prisma.rolePermission.create({
            data: {
                roleId: superAdminRole.id,
                permissionId: permission.id
            }
        });
    }
}

/**
 * 创建用户和机构
 */
async function createUsersAndInstitutions(roles) {
    console.log('👤 创建用户和机构...');

    const { superAdminRole, teacherRole, orgAdminRole } = roles;
    const password = bcrypt.hashSync('123456', 10);

    // 创建超级管理员用户
    const superAdminUser = await prisma.user.upsert({
        where: { account: 'superadmin' },
        update: {},
        create: {
            id: 'super-admin-id',
            name: '超级管理员',
            password: password.toString(),
            account: 'superadmin',
            phone: '***********',
        },
    });

    // 创建机构管理员用户
    const orgAdminUser = await prisma.user.upsert({
        where: { account: 'orgadmin' },
        update: {},
        create: {
            id: 'org-admin-id',
            name: '机构管理员',
            password: password.toString(),
            account: 'orgadmin',
            phone: '***********',
        },
    });

    // 创建教师用户
    const teacherUser = await prisma.user.upsert({
        where: { account: 'teacher' },
        update: {},
        create: {
            id: 'teacher-user-id',
            name: '教师用户',
            password: password.toString(),
            account: 'teacher',
            phone: '***********',
        },
    });

    // 创建机构
    const institution = await prisma.institution.upsert({
        where: { name: '测试机构' },
        update: { userId: orgAdminUser.id },
        create: {
            name: '测试机构',
            userId: orgAdminUser.id,
        },
    });

    // 创建用户机构关联
    // 先删除现有关联，然后创建新的
    await prisma.userInstitution.deleteMany({
        where: {
            OR: [
                { userId: teacherUser.id, institutionId: institution.id },
                { userId: orgAdminUser.id, institutionId: institution.id }
            ]
        }
    });

    await prisma.userInstitution.createMany({
        data: [
            {
                userId: teacherUser.id,
                institutionId: institution.id,
            },
            {
                userId: orgAdminUser.id,
                institutionId: institution.id,
            }
        ],
        skipDuplicates: true
    });

    // 分配角色给用户
    await prisma.userRole.deleteMany({
        where: {
            userId: {
                in: [superAdminUser.id, teacherUser.id, orgAdminUser.id]
            }
        }
    });

    await prisma.userRole.createMany({
        data: [
            {
                userId: superAdminUser.id,
                roleId: superAdminRole.id,
            },
            {
                userId: teacherUser.id,
                roleId: teacherRole.id,
            },
            {
                userId: orgAdminUser.id,
                roleId: orgAdminRole.id,
            }
        ],
        skipDuplicates: true,
    });

    return { superAdminUser, teacherUser, orgAdminUser, institution };
}

/**
 * 创建菜单
 */
async function createMenus() {
    console.log('📋 创建菜单...');

    // 创建主菜单
    await prisma.menu.createMany({
        data: [
            {
                id: 'a7e46980-6adc-4af3-8553-d419f110cc44',
                name: '仪表盘',
                path: '/dashboard',
                icon: 'ChartBarIcon',
                component: 'Dashboard',
                sort: 1,
                permissionCode: 'dashboard:access',
            },
            {
                id: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                name: '教务中心',
                icon: 'AcademicCapIcon',
                permissionCode: 'academic:access',
                sort: 2
            },
            {
                id: 'c7e46980-6adc-4af3-8553-d419f110cc44',
                name: '招生中心',
                icon: 'BriefcaseIcon',
                permissionCode: 'enrollment:access',
                sort: 3
            },
            {
                id: 'd7e46980-6adc-4af3-8553-d419f110cc44',
                name: '财务中心',
                icon: 'CurrencyDollarIcon',
                permissionCode: 'finance:access',
                sort: 4
            },
            {
                id: 'e7e46980-6adc-4af3-8553-d419f110cc44',
                name: '数据中心',
                icon: 'ChartPieIcon',
                permissionCode: 'data:access',
                sort: 5
            },
            {
                id: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                name: '机构中心',
                icon: 'BuildingOfficeIcon',
                permissionCode: 'org:access',
                sort: 6
            },
            {
                id: 'h7e46980-6adc-4af3-8553-d419f110cc44',
                name: '通知管理',
                icon: 'BellIcon',
                permissionCode: 'notification:access',
                sort: 7
            },

        ]
    });

    // 创建子菜单
    await prisma.menu.createMany({
        data: [
            // 教务中心子菜单
            {
                name: '学员管理',
                path: '/academic/students',
                icon: 'UserGroupIcon',
                sort: 1,
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'student:read',
            },
            {
                name: '班级管理',
                path: '/academic/classes',
                sort: 2,
                icon: 'BuildingOfficeIcon',
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'class:read',
            },
            {
                name: '课表管理',
                path: '/academic/schedule',
                sort: 3,
                icon: 'ChartPieIcon',
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'schedule:read',
            },
            {
                name: '课程管理',
                path: '/academic/courses',
                sort: 4,
                icon: 'ChartBarIcon',
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'course:read',
            },
            {
                name: '套餐管理',
                path: '/academic/product',
                sort: 5,
                icon: 'ShoppingBagIcon',
                parentId: 'b7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'package:read',
            },
            // 招生中心子菜单
            {
                name: '跟进管理',
                path: '/enrollment/followUp',
                icon: 'TagIcon',
                sort: 1,
                parentId: 'c7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'followUp:read',
            },
            // 财务中心子菜单
            {
                name: '销售记录',
                path: '/finance/receipt',
                icon: 'ShoppingBagIcon',
                sort: 1,
                parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'finance:sales:read',
            },
            {
                name: '收银记账',
                path: '/finance/cashier',
                icon: 'CurrencyDollarIcon',
                sort: 2,
                parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'finance:bill:read',
            },
            {
                name: '工资管理',
                path: '/finance/wages',
                icon: 'CurrencyDollarIcon',
                sort: 3,
                parentId: 'd7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'finance:salary:read',
            },
            // 数据中心子菜单
            {
                name: '教务数据',
                path: '/data/education',
                icon: 'ChartPieIcon',
                sort: 1,
                parentId: 'e7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'data:academic:read',
            },
            {
                name: '销售数据',
                path: '/data/sales',
                icon: 'CurrencyDollarIcon',
                sort: 2,
                parentId: 'e7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'data:sales:read',
            },
            // 机构中心子菜单
            {
                name: '机构信息',
                path: '/organization/info',
                icon: 'InformationCircleIcon',
                sort: 1,
                parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'org:info:read',
            },
            {
                name: '员工管理',
                path: '/organization/staff',
                icon: 'UserGroupIcon',
                sort: 2,
                parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'org:staff:read',
            },
            {
                name: '教室管理',
                path: '/organization/classrooms',
                icon: 'BuildingOfficeIcon',
                sort: 3,
                parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'org:classroom:read',
            },
            {
                name: '操作日志',
                path: '/organization/log',
                icon: 'InboxIcon',
                sort: 4,
                parentId: 'f7e46980-6adc-4af3-8553-d419f110cc44',
                permissionCode: 'org:log:read',
            },
           
        ]
    });
}

/**
 * 主函数 - 执行所有种子数据创建
 */
async function main() {
    console.log('🌱 开始数据库种子初始化...');

    try {
        // 1. 清理现有数据
        console.log('🧹 清理现有数据...');
        await cleanupExistingData();
        console.log('✅ 数据清理完成');

        // 2. 创建权限
        await createPermissions();
        console.log('✅ 权限创建完成');

        // 3. 创建角色
        const roles = await createRoles();
        console.log('✅ 角色创建完成');

        // 4. 分配角色权限
        await assignRolePermissions(roles);
        console.log('✅ 角色权限分配完成');

        // 5. 创建用户和机构
        const users = await createUsersAndInstitutions(roles);
        console.log('✅ 用户和机构创建完成');

        // 6. 创建菜单
        await createMenus();
        console.log('✅ 菜单创建完成');

        console.log('🎉 数据库种子初始化完成！');
        console.log('📋 创建的用户账号:');
        console.log('  - 超级管理员: superadmin / 123456');
        console.log('  - 机构管理员: orgadmin / 123456');
        console.log('  - 教师用户: teacher / 123456');

    } catch (error) {
        console.error('❌ 种子数据创建失败:', error);
        throw error;
    }
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
})
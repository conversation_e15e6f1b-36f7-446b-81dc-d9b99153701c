import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 完整系统测试脚本
 * 包含数据库重置、权限验证和API功能测试
 */

// 执行命令的辅助函数
function runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
        console.log(`🔧 执行命令: ${command} ${args.join(' ')}`);
        
        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true,
            ...options
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve(code);
            } else {
                reject(new Error(`命令执行失败，退出码: ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

// 执行Node.js脚本
function runScript(scriptPath) {
    return runCommand('node', [scriptPath]);
}

// 主测试流程
async function runFullSystemTest() {
    console.log('🚀 开始完整系统测试...\n');
    
    const startTime = Date.now();

    try {
        // 步骤1: 重新初始化数据库
        console.log('📋 步骤 1/4: 重新初始化数据库');
        console.log('=' .repeat(60));
        await runCommand('npm', ['run', 'seed']);
        console.log('✅ 数据库初始化完成\n');

        // 步骤2: 清理Redis缓存
        console.log('📋 步骤 2/4: 清理Redis缓存');
        console.log('=' .repeat(60));
        await runScript(join(__dirname, 'clear-redis.js'));
        console.log('✅ Redis缓存清理完成\n');

        // 步骤3: 验证角色权限分配
        console.log('📋 步骤 3/4: 验证角色权限分配');
        console.log('=' .repeat(60));
        await runScript(join(__dirname, 'verify-role-permissions.js'));
        console.log('✅ 角色权限验证完成\n');

        // 步骤4: 提示API测试
        console.log('📋 步骤 4/4: API功能测试');
        console.log('=' .repeat(60));
        console.log('⚠️  API测试需要服务器运行，请按以下步骤操作:');
        console.log('   1. 在另一个终端运行: npm run dev');
        console.log('   2. 等待服务器启动完成');
        console.log('   3. 运行: node scripts/test-api-functionality.js');
        console.log('');

        // 计算总耗时
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        console.log('🎉 完整系统测试完成！');
        console.log(`⏱️  总耗时: ${duration} 秒`);
        console.log('');
        console.log('📊 测试结果总结:');
        console.log('   ✅ 数据库种子数据重新初始化');
        console.log('   ✅ Redis缓存清理');
        console.log('   ✅ 角色权限分配验证');
        console.log('   ⏳ API功能测试 (需手动执行)');
        console.log('');
        console.log('🔑 测试账号信息:');
        console.log('   - 超级管理员: superadmin / 123456');
        console.log('   - 机构管理员: orgadmin / 123456');
        console.log('   - 教师用户: teacher / 123456');

    } catch (error) {
        console.error('❌ 系统测试失败:', error.message);
        process.exit(1);
    }
}

// 执行测试
runFullSystemTest();

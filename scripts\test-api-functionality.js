/**
 * API功能测试脚本
 * 测试登录、权限验证等核心功能
 */

const API_BASE = 'http://localhost:3000';

// 测试用户凭据
const testUsers = [
    { account: 'superadmin', password: '123456', role: '超级管理员' },
    { account: 'orgadmin', password: '123456', role: '机构管理员' },
    { account: 'teacher', password: '123456', role: '教师' }
];

// 检查服务器是否运行
async function checkServer() {
    try {
        const response = await fetch(`${API_BASE}/health`);
        return response.ok;
    } catch (error) {
        return false;
    }
}

// 测试登录功能
async function testLogin(user) {
    try {
        console.log(`🔐 测试 ${user.role} 登录...`);
        
        const response = await fetch(`${API_BASE}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                account: user.account,
                password: user.password
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log(`   ✅ ${user.role} 登录成功`);
            console.log(`   📋 用户信息: ${data.user?.name || 'N/A'}`);
            console.log(`   🎫 Token: ${data.accessToken ? '已获取' : '未获取'}`);
            return {
                success: true,
                token: data.accessToken,
                user: data.user
            };
        } else {
            const error = await response.text();
            console.log(`   ❌ ${user.role} 登录失败: ${response.status} - ${error}`);
            return { success: false, error };
        }
    } catch (error) {
        console.log(`   ❌ ${user.role} 登录异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 测试权限验证
async function testPermissions(token, userRole) {
    console.log(`🔍 测试 ${userRole} 权限验证...`);
    
    // 测试不同的API端点
    const testEndpoints = [
        { path: '/api/menus', description: '获取菜单' },
        { path: '/api/users/profile', description: '获取用户信息' },
        { path: '/api/roles', description: '获取角色列表' },
    ];

    for (const endpoint of testEndpoints) {
        try {
            const response = await fetch(`${API_BASE}${endpoint.path}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                console.log(`   ✅ ${endpoint.description}: 成功 (${response.status})`);
            } else if (response.status === 403) {
                console.log(`   🚫 ${endpoint.description}: 权限不足 (${response.status})`);
            } else if (response.status === 401) {
                console.log(`   🔒 ${endpoint.description}: 未授权 (${response.status})`);
            } else {
                console.log(`   ⚠️  ${endpoint.description}: 其他错误 (${response.status})`);
            }
        } catch (error) {
            console.log(`   ❌ ${endpoint.description}: 请求异常 - ${error.message}`);
        }
    }
}

// 主测试函数
async function runTests() {
    console.log('🧪 开始API功能测试...\n');

    // 检查服务器状态
    const serverRunning = await checkServer();
    if (!serverRunning) {
        console.log('❌ 服务器未运行，请先启动服务器: npm run dev');
        return;
    }
    console.log('✅ 服务器运行正常\n');

    // 测试每个用户的登录和权限
    for (const user of testUsers) {
        console.log(`\n${'='.repeat(50)}`);
        console.log(`测试用户: ${user.role} (${user.account})`);
        console.log(`${'='.repeat(50)}`);

        // 测试登录
        const loginResult = await testLogin(user);
        
        if (loginResult.success) {
            // 测试权限
            await testPermissions(loginResult.token, user.role);
        }

        console.log(''); // 空行分隔
    }

    console.log('\n🎉 API功能测试完成！');
    console.log('\n📝 测试说明:');
    console.log('   ✅ 成功 - 功能正常');
    console.log('   🚫 权限不足 - 正常的权限控制');
    console.log('   🔒 未授权 - Token验证失败');
    console.log('   ❌ 异常 - 需要检查的问题');
}

// 执行测试
runTests().catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
});

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

/**
 * 直接测试学生创建的脚本
 * 模拟studentService.addStudent的完整逻辑
 */

async function testStudentCreationDirect() {
    console.log('🧪 开始直接测试学生创建...\n');

    try {
        // 模拟请求数据
        const requestData = {
            name: "测试学生",
            gender: "male",
            phone: "13800138000",
            idCard: "360111201405022070",
            birthday: "1559318400000",
            source: "朋友推荐",
            sourceDesc: "测试描述",
            referrer: "推荐人",
            intention: "A",
            follower: "org-admin-id",
            address: "测试地址",
            remarks: "测试备注",
            type: "formal"
        };

        // 模拟认证用户信息
        const mockUser = {
            id: "org-admin-id",
            institutionId: "3a2be69a-9f60-4ff8-9dea-60ea0c55249a"
        };

        console.log('📋 请求数据:');
        console.log(JSON.stringify(requestData, null, 2));
        console.log('\n👤 认证用户信息:');
        console.log(JSON.stringify(mockUser, null, 2));

        // 验证所有外键是否存在
        console.log('\n🔍 验证外键约束...');

        // 1. 验证机构
        const institution = await prisma.institution.findUnique({
            where: { id: mockUser.institutionId },
            select: { id: true, name: true }
        });

        if (!institution) {
            console.log(`❌ 机构不存在: ${mockUser.institutionId}`);
            return;
        }
        console.log(`✅ 机构存在: ${institution.name} (${institution.id})`);

        // 2. 验证操作员
        const operator = await prisma.user.findUnique({
            where: { id: mockUser.id },
            select: { id: true, name: true, account: true }
        });

        if (!operator) {
            console.log(`❌ 操作员不存在: ${mockUser.id}`);
            return;
        }
        console.log(`✅ 操作员存在: ${operator.name} (${operator.account})`);

        // 3. 验证跟进人
        const follower = await prisma.user.findUnique({
            where: { id: requestData.follower },
            select: { id: true, name: true, account: true }
        });

        if (!follower) {
            console.log(`❌ 跟进人不存在: ${requestData.follower}`);
            return;
        }
        console.log(`✅ 跟进人存在: ${follower.name} (${follower.account})`);

        // 准备创建数据
        const studentData = {
            id: uuidv4(),
            name: requestData.name,
            phone: requestData.phone,
            gender: requestData.gender,
            birthday: requestData.birthday ? BigInt(requestData.birthday) : null,
            sourceDesc: requestData.sourceDesc,
            intentLevel: requestData.intention,
            followerId: requestData.follower,
            source: requestData.source,
            referrer: requestData.referrer,
            address: requestData.address,
            idCard: requestData.idCard,
            remarks: requestData.remarks,
            institutionId: mockUser.institutionId,
            type: requestData.type,
            operatorId: mockUser.id
        };

        console.log('\n📝 准备创建的学生数据:');
        console.log(JSON.stringify(studentData, (key, value) => 
            typeof value === 'bigint' ? value.toString() : value, 2));

        // 尝试创建学生
        console.log('\n🚀 开始创建学生...');
        
        const result = await prisma.student.create({
            data: studentData,
            include: {
                institution: {
                    select: { name: true }
                },
                operator: {
                    select: { name: true, account: true }
                },
                follower: {
                    select: { name: true, account: true }
                }
            }
        });

        console.log('✅ 学生创建成功！');
        console.log('\n📋 创建结果:');
        console.log(`   学生ID: ${result.id}`);
        console.log(`   姓名: ${result.name}`);
        console.log(`   电话: ${result.phone}`);
        console.log(`   性别: ${result.gender}`);
        console.log(`   类型: ${result.type}`);
        console.log(`   机构: ${result.institution.name}`);
        console.log(`   操作员: ${result.operator.name} (${result.operator.account})`);
        console.log(`   跟进人: ${result.follower.name} (${result.follower.account})`);
        console.log(`   创建时间: ${result.createdAt}`);

        // 清理测试数据
        console.log('\n🧹 清理测试数据...');
        await prisma.student.delete({
            where: { id: result.id }
        });
        console.log('✅ 测试数据清理完成');

        console.log('\n🎉 学生创建测试成功！');

    } catch (error) {
        console.error('❌ 学生创建测试失败:', error);
        
        // 详细错误分析
        if (error.code === 'P2003') {
            console.log('\n💡 外键约束错误分析:');
            console.log('   这是Prisma的外键约束违反错误');
            console.log('   可能的原因:');
            console.log('   1. followerId指向的用户不存在');
            console.log('   2. institutionId指向的机构不存在');
            console.log('   3. operatorId指向的用户不存在');
            console.log('   4. 数据库中的外键约束设置有问题');
        } else if (error.code === 'P2002') {
            console.log('\n💡 唯一约束错误分析:');
            console.log('   可能存在重复的数据');
        } else {
            console.log('\n💡 其他错误:');
            console.log(`   错误代码: ${error.code || 'N/A'}`);
            console.log(`   错误消息: ${error.message}`);
        }
        
        throw error;
    }
}

// 执行测试
testStudentCreationDirect().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
});

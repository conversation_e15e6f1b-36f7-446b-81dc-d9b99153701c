import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 检查外键约束脚本
 * 验证数据库中的外键关系是否正确
 */

async function checkForeignKeys() {
    console.log('🔍 开始检查外键约束...\n');

    try {
        // 检查用户表
        console.log('👥 检查用户表:');
        const users = await prisma.user.findMany({
            select: {
                id: true,
                account: true,
                name: true
            }
        });

        console.log(`   用户总数: ${users.length}`);
        users.forEach(user => {
            console.log(`   - ${user.account} (${user.name}) - ID: ${user.id}`);
        });

        // 检查机构表
        console.log('\n🏢 检查机构表:');
        const institutions = await prisma.institution.findMany({
            select: {
                id: true,
                name: true,
                userId: true
            }
        });

        console.log(`   机构总数: ${institutions.length}`);
        institutions.forEach(institution => {
            console.log(`   - ${institution.name} - ID: ${institution.id} - 管理员: ${institution.userId}`);
        });

        // 检查学生表中的外键
        console.log('\n🎓 检查学生表外键约束:');
        const students = await prisma.student.findMany({
            select: {
                id: true,
                name: true,
                followerId: true,
                institutionId: true,
                operatorId: true
            }
        });

        console.log(`   学生总数: ${students.length}`);
        
        if (students.length > 0) {
            for (const student of students) {
                console.log(`   学生: ${student.name} (${student.id})`);
                console.log(`     - followerId: ${student.followerId || 'null'}`);
                console.log(`     - institutionId: ${student.institutionId}`);
                console.log(`     - operatorId: ${student.operatorId || 'null'}`);

                // 验证外键是否存在
                if (student.followerId) {
                    const follower = users.find(u => u.id === student.followerId);
                    if (!follower) {
                        console.log(`     ❌ followerId ${student.followerId} 不存在于用户表中`);
                    } else {
                        console.log(`     ✅ follower: ${follower.name} (${follower.account})`);
                    }
                }

                if (student.institutionId) {
                    const institution = institutions.find(i => i.id === student.institutionId);
                    if (!institution) {
                        console.log(`     ❌ institutionId ${student.institutionId} 不存在于机构表中`);
                    } else {
                        console.log(`     ✅ institution: ${institution.name}`);
                    }
                }

                if (student.operatorId) {
                    const operator = users.find(u => u.id === student.operatorId);
                    if (!operator) {
                        console.log(`     ❌ operatorId ${student.operatorId} 不存在于用户表中`);
                    } else {
                        console.log(`     ✅ operator: ${operator.name} (${operator.account})`);
                    }
                }
                console.log('');
            }
        } else {
            console.log('   暂无学生数据');
        }

        // 提供修复建议
        console.log('\n💡 修复建议:');
        console.log('   如果要创建学生记录，请确保:');
        console.log('   1. follower字段使用有效的用户ID或设为null');
        console.log('   2. institutionId使用有效的机构ID');
        console.log('   3. operatorId使用有效的用户ID或设为null');
        console.log('');
        console.log('   可用的用户ID:');
        users.forEach(user => {
            console.log(`     - ${user.id} (${user.account} - ${user.name})`);
        });
        console.log('');
        console.log('   可用的机构ID:');
        institutions.forEach(institution => {
            console.log(`     - ${institution.id} (${institution.name})`);
        });

        console.log('\n✅ 外键约束检查完成！');

    } catch (error) {
        console.error('❌ 检查过程中出现错误:', error);
        throw error;
    }
}

// 执行检查
checkForeignKeys().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
});

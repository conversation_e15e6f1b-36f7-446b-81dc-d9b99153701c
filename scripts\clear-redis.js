import { redis, redisWebSocket, redisTask, redisSystem } from "../config/redis.js";

/**
 * 清理Redis缓存脚本
 * 清空所有Redis数据库中的缓存数据
 */

async function clearRedisCache() {
    console.log('🧹 开始清理Redis缓存...');
    
    try {
        // 清理主缓存实例 (DB 1)
        console.log('清理主缓存 (DB 1)...');
        await redis.flushdb();
        
        // 清理WebSocket缓存 (DB 10)
        console.log('清理WebSocket缓存 (DB 10)...');
        await redisWebSocket.flushdb();
        
        // 清理任务队列缓存 (DB 11)
        console.log('清理任务队列缓存 (DB 11)...');
        await redisTask.flushdb();
        
        // 清理系统配置缓存 (DB 0)
        console.log('清理系统配置缓存 (DB 0)...');
        await redisSystem.flushdb();
        
        console.log('✅ Redis缓存清理完成！');
        
    } catch (error) {
        console.error('❌ Redis缓存清理失败:', error);
        throw error;
    } finally {
        // 关闭Redis连接
        await Promise.all([
            redis.quit(),
            redisWebSocket.quit(),
            redisTask.quit(),
            redisSystem.quit()
        ]);
    }
}

// 执行清理
clearRedisCache().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
});

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

/**
 * 测试数据转换问题的脚本
 * 特别是birthday字段的BigInt转换
 */

async function testDataConversion() {
    console.log('🧪 开始测试数据转换...\n');

    try {
        // 测试不同的birthday值
        const testCases = [
            { name: "字符串数字", birthday: "1559318400000" },
            { name: "数字", birthday: 1559318400000 },
            { name: "BigInt", birthday: BigInt("1559318400000") },
            { name: "null", birthday: null },
            { name: "undefined", birthday: undefined }
        ];

        for (const testCase of testCases) {
            console.log(`📋 测试案例: ${testCase.name}`);
            console.log(`   原始值: ${testCase.birthday} (类型: ${typeof testCase.birthday})`);

            try {
                // 模拟controller中的转换
                let convertedBirthday;
                if (testCase.birthday !== null && testCase.birthday !== undefined) {
                    convertedBirthday = Number(testCase.birthday);
                    console.log(`   Number转换后: ${convertedBirthday} (类型: ${typeof convertedBirthday})`);
                } else {
                    convertedBirthday = testCase.birthday;
                    console.log(`   保持原值: ${convertedBirthday}`);
                }

                // 模拟service中的BigInt转换
                let finalBirthday;
                if (convertedBirthday !== null && convertedBirthday !== undefined) {
                    finalBirthday = BigInt(convertedBirthday);
                    console.log(`   BigInt转换后: ${finalBirthday} (类型: ${typeof finalBirthday})`);
                } else {
                    finalBirthday = null;
                    console.log(`   最终值: ${finalBirthday}`);
                }

                // 尝试创建学生记录
                const studentData = {
                    id: uuidv4(),
                    name: `测试学生-${testCase.name}`,
                    phone: `1380013800${Math.floor(Math.random() * 10)}`,
                    gender: "male",
                    birthday: finalBirthday,
                    sourceDesc: "测试",
                    intentLevel: "A",
                    followerId: "org-admin-id",
                    source: "测试",
                    referrer: "测试",
                    address: "测试地址",
                    idCard: "360111201405022070",
                    remarks: "测试备注",
                    institutionId: "3a2be69a-9f60-4ff8-9dea-60ea0c55249a",
                    type: "formal",
                    operatorId: "org-admin-id"
                };

                const result = await prisma.student.create({
                    data: studentData,
                    select: {
                        id: true,
                        name: true,
                        birthday: true
                    }
                });

                console.log(`   ✅ 创建成功: ${result.name}`);
                console.log(`   数据库中的birthday: ${result.birthday} (类型: ${typeof result.birthday})`);

                // 清理测试数据
                await prisma.student.delete({
                    where: { id: result.id }
                });

            } catch (error) {
                console.log(`   ❌ 创建失败: ${error.message}`);
                if (error.code) {
                    console.log(`   错误代码: ${error.code}`);
                }
            }

            console.log(''); // 空行分隔
        }

        // 测试原始请求数据的完整转换流程
        console.log('🔄 测试完整的请求数据转换流程...\n');

        const originalRequestBody = {
            "name": "aaa",
            "gender": "secret",
            "phone": "13145211314",
            "idCard": "360111201405022070",
            "birthday": "1559318400000",
            "source": "朋友推荐",
            "sourceDesc": "小白的弟弟",
            "referrer": "小白",
            "intention": "A",
            "follower": "org-admin-id",
            "address": "aaaaaa",
            "remarks": "",
            "type": "formal"
        };

        console.log('📋 原始请求体:');
        console.log(JSON.stringify(originalRequestBody, null, 2));

        // 模拟controller的处理
        const controllerProcessed = {
            name: originalRequestBody.name,
            gender: originalRequestBody.gender,
            phone: originalRequestBody.phone,
            birthday: Number(originalRequestBody.birthday),
            sourceDesc: originalRequestBody.sourceDesc,
            intention: originalRequestBody.intention,
            follower: originalRequestBody.follower,
            source: originalRequestBody.source,
            referrer: originalRequestBody.referrer,
            address: originalRequestBody.address,
            idCard: originalRequestBody.idCard,
            remarks: originalRequestBody.remarks,
            type: originalRequestBody.type,
            institutionId: "3a2be69a-9f60-4ff8-9dea-60ea0c55249a",
            operatorId: "org-admin-id"
        };

        console.log('\n📋 Controller处理后:');
        console.log(JSON.stringify(controllerProcessed, null, 2));

        // 模拟service的处理
        const serviceProcessed = {
            id: uuidv4(),
            name: controllerProcessed.name,
            phone: controllerProcessed.phone,
            gender: controllerProcessed.gender,
            birthday: controllerProcessed.birthday ? BigInt(controllerProcessed.birthday) : null,
            sourceDesc: controllerProcessed.sourceDesc,
            intentLevel: controllerProcessed.intention,
            followerId: controllerProcessed.follower || null,
            source: controllerProcessed.source,
            referrer: controllerProcessed.referrer,
            address: controllerProcessed.address,
            idCard: controllerProcessed.idCard,
            remarks: controllerProcessed.remarks,
            institutionId: controllerProcessed.institutionId,
            type: controllerProcessed.type,
            operatorId: controllerProcessed.operatorId
        };

        console.log('\n📋 Service处理后:');
        console.log(JSON.stringify(serviceProcessed, (key, value) => 
            typeof value === 'bigint' ? value.toString() : value, 2));

        // 尝试创建
        console.log('\n🚀 尝试创建学生...');
        const finalResult = await prisma.student.create({
            data: serviceProcessed,
            include: {
                institution: { select: { name: true } },
                operator: { select: { name: true } },
                follower: { select: { name: true } }
            }
        });

        console.log('✅ 完整流程测试成功！');
        console.log(`   学生ID: ${finalResult.id}`);
        console.log(`   姓名: ${finalResult.name}`);
        console.log(`   生日: ${finalResult.birthday}`);

        // 清理
        await prisma.student.delete({
            where: { id: finalResult.id }
        });

        console.log('\n🎉 数据转换测试完成！');

    } catch (error) {
        console.error('❌ 数据转换测试失败:', error);
        throw error;
    }
}

// 执行测试
testDataConversion().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
});

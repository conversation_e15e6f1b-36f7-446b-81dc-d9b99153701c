/**
 * 诊断学生创建问题的综合脚本
 * 检查所有可能的问题点
 */

const API_BASE = 'http://localhost:3000';

// 检查服务器状态
async function checkServerStatus() {
    console.log('🔍 检查服务器状态...');
    
    try {
        const response = await fetch(`${API_BASE}/health`);
        if (response.ok) {
            console.log('✅ 服务器运行正常');
            return true;
        } else {
            console.log(`❌ 服务器响应异常: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ 无法连接到服务器: ${error.message}`);
        console.log('💡 请确保服务器已启动: npm run dev');
        return false;
    }
}

// 测试登录
async function testLogin() {
    console.log('\n🔐 测试登录...');
    
    try {
        const response = await fetch(`${API_BASE}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                account: 'orgadmin',
                password: '123456'
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('✅ 登录成功');
            return data.accessToken;
        } else {
            const error = await response.text();
            console.log(`❌ 登录失败: ${response.status} - ${error}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ 登录异常: ${error.message}`);
        return null;
    }
}

// 测试用户信息获取
async function testUserInfo(token) {
    console.log('\n👤 测试用户信息获取...');
    
    try {
        const response = await fetch(`${API_BASE}/api/users/profile`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('✅ 用户信息获取成功');
            console.log(`   用户ID: ${data.user?.id || 'N/A'}`);
            console.log(`   机构ID: ${data.user?.institutionId || 'N/A'}`);
            return data.user;
        } else {
            const error = await response.text();
            console.log(`❌ 用户信息获取失败: ${response.status} - ${error}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ 用户信息获取异常: ${error.message}`);
        return null;
    }
}

// 测试学生创建
async function testStudentCreation(token) {
    console.log('\n👨‍🎓 测试学生创建...');
    
    const studentData = {
        "name": "API测试学生",
        "gender": "male",
        "phone": "13800138001",
        "idCard": "360111201405022071",
        "birthday": "1559318400000",
        "source": "朋友推荐",
        "sourceDesc": "API测试",
        "referrer": "推荐人",
        "intention": "A",
        "follower": "org-admin-id",
        "address": "测试地址",
        "remarks": "API测试备注",
        "type": "formal"
    };

    console.log('📋 请求数据:');
    console.log(JSON.stringify(studentData, null, 2));

    try {
        const response = await fetch(`${API_BASE}/api/students/add`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(studentData)
        });

        console.log(`\n📡 响应状态: ${response.status}`);
        console.log(`📡 响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);

        const responseText = await response.text();
        console.log(`📡 响应内容: ${responseText}`);

        if (response.ok) {
            console.log('✅ 学生创建成功');
            try {
                const result = JSON.parse(responseText);
                return { success: true, data: result };
            } catch (e) {
                return { success: true, data: responseText };
            }
        } else {
            console.log(`❌ 学生创建失败: ${response.status}`);
            return { success: false, error: responseText, status: response.status };
        }
    } catch (error) {
        console.log(`❌ 请求异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 主诊断函数
async function diagnoseStudentCreationIssue() {
    console.log('🔧 开始诊断学生创建问题...\n');

    // 1. 检查服务器状态
    const serverOk = await checkServerStatus();
    if (!serverOk) {
        console.log('\n❌ 服务器未运行，请先启动服务器后再次运行此脚本');
        return;
    }

    // 2. 测试登录
    const token = await testLogin();
    if (!token) {
        console.log('\n❌ 登录失败，无法继续测试');
        return;
    }

    // 3. 测试用户信息
    const userInfo = await testUserInfo(token);
    if (!userInfo) {
        console.log('\n❌ 用户信息获取失败，可能是认证问题');
        return;
    }

    // 4. 测试学生创建
    const createResult = await testStudentCreation(token);
    
    console.log('\n📊 诊断结果总结:');
    console.log('=' .repeat(50));
    
    if (createResult.success) {
        console.log('🎉 学生创建功能正常！');
        console.log('   所有测试都通过了，API工作正常');
    } else {
        console.log('❌ 学生创建功能异常');
        console.log(`   错误状态: ${createResult.status || 'N/A'}`);
        console.log(`   错误信息: ${createResult.error || 'N/A'}`);
        
        console.log('\n💡 可能的解决方案:');
        console.log('   1. 检查数据库连接');
        console.log('   2. 检查外键约束');
        console.log('   3. 检查认证中间件');
        console.log('   4. 查看服务器日志');
        console.log('   5. 运行: node scripts/check-foreign-keys.js');
    }
}

// 执行诊断
diagnoseStudentCreationIssue().catch(error => {
    console.error('诊断执行失败:', error);
    process.exit(1);
});

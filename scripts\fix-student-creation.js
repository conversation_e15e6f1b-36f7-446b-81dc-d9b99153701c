import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 修复学生创建问题的脚本
 * 检查并修复用户机构关联问题
 */

async function fixStudentCreation() {
    console.log('🔧 开始修复学生创建问题...\n');

    try {
        // 检查用户机构关联
        console.log('👥 检查用户机构关联:');
        const userInstitutions = await prisma.userInstitution.findMany({
            include: {
                user: {
                    select: {
                        id: true,
                        account: true,
                        name: true
                    }
                },
                institution: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        console.log(`   用户机构关联总数: ${userInstitutions.length}`);
        userInstitutions.forEach(ui => {
            console.log(`   - ${ui.user.account} (${ui.user.name}) → ${ui.institution.name}`);
            console.log(`     用户ID: ${ui.user.id}`);
            console.log(`     机构ID: ${ui.institution.id}`);
            console.log('');
        });

        // 检查机构管理员用户的详细信息
        console.log('🔍 检查机构管理员用户详情:');
        const orgAdminUser = await prisma.user.findUnique({
            where: { id: 'org-admin-id' },
            include: {
                userInstitutions: {
                    include: {
                        institution: true
                    }
                }
            }
        });

        if (orgAdminUser) {
            console.log(`   用户: ${orgAdminUser.name} (${orgAdminUser.account})`);
            console.log(`   用户ID: ${orgAdminUser.id}`);
            console.log(`   关联机构数: ${orgAdminUser.userInstitutions.length}`);
            
            if (orgAdminUser.userInstitutions.length > 0) {
                orgAdminUser.userInstitutions.forEach(ui => {
                    console.log(`   - 机构: ${ui.institution.name} (ID: ${ui.institution.id})`);
                });
            } else {
                console.log('   ❌ 该用户没有关联任何机构！');
            }
        } else {
            console.log('   ❌ 找不到机构管理员用户！');
        }

        // 提供修复方案
        console.log('\n💡 修复方案:');
        
        if (userInstitutions.length === 0) {
            console.log('   ❌ 没有用户机构关联，需要创建关联');
            
            // 获取机构信息
            const institution = await prisma.institution.findFirst();
            if (institution) {
                console.log(`   🔧 正在为机构管理员创建机构关联...`);
                
                await prisma.userInstitution.create({
                    data: {
                        userId: 'org-admin-id',
                        institutionId: institution.id,
                        isAdmin: true
                    }
                });
                
                console.log(`   ✅ 已创建用户机构关联: org-admin-id → ${institution.id}`);
            }
        }

        // 验证修复结果
        console.log('\n🔍 验证修复结果:');
        const updatedUserInstitutions = await prisma.userInstitution.findMany({
            where: { userId: 'org-admin-id' },
            include: {
                institution: true
            }
        });

        if (updatedUserInstitutions.length > 0) {
            console.log('   ✅ 机构管理员现在有以下机构关联:');
            updatedUserInstitutions.forEach(ui => {
                console.log(`     - ${ui.institution.name} (ID: ${ui.institution.id})`);
            });
        } else {
            console.log('   ❌ 机构管理员仍然没有机构关联');
        }

        // 提供测试建议
        console.log('\n📝 测试建议:');
        console.log('   现在可以使用以下参数创建学生:');
        console.log('   {');
        console.log('     "name": "测试学生",');
        console.log('     "gender": "male",');
        console.log('     "phone": "13800138000",');
        console.log('     "birthday": "1559318400000",');
        console.log('     "source": "朋友推荐",');
        console.log('     "sourceDesc": "测试描述",');
        console.log('     "referrer": "推荐人",');
        console.log('     "intention": "A",');
        console.log('     "follower": "org-admin-id",');
        console.log('     "address": "测试地址",');
        console.log('     "remarks": "测试备注",');
        console.log('     "type": "formal"');
        console.log('   }');

        console.log('\n✅ 学生创建问题修复完成！');

    } catch (error) {
        console.error('❌ 修复过程中出现错误:', error);
        throw error;
    }
}

// 执行修复
fixStudentCreation().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
});

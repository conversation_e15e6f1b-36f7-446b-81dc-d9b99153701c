/**
 * 验证修复效果的测试脚本
 * 测试修复后的学生创建功能
 */

const API_BASE = 'http://localhost:3000';

// 检查服务器状态
async function checkServer() {
    try {
        const response = await fetch(`${API_BASE}/health`);
        return response.ok;
    } catch (error) {
        return false;
    }
}

// 登录获取token
async function login() {
    try {
        console.log('🔐 正在登录...');
        
        const response = await fetch(`${API_BASE}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                account: 'orgadmin',
                password: '123456'
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('✅ 登录成功');
            return data.accessToken;
        } else {
            const error = await response.text();
            console.log(`❌ 登录失败: ${response.status} - ${error}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ 登录异常: ${error.message}`);
        return null;
    }
}

// 测试学生创建（使用原始的错误数据）
async function testStudentCreation(token) {
    console.log('\n👨‍🎓 测试学生创建（使用原始错误数据）...');
    
    // 使用与错误日志完全相同的数据
    const studentData = {
        "name": "aaa",
        "gender": "secret",
        "phone": "***********",
        "idCard": "360111201405022070",
        "birthday": "*************",
        "source": "朋友推荐",
        "sourceDesc": "小白的弟弟",
        "referrer": "小白",
        "intention": "A",
        "follower": "org-admin-id",
        "address": "aaaaaa",
        "remarks": "",
        "type": "formal"
    };

    console.log('📋 请求数据（与错误日志相同）:');
    console.log(JSON.stringify(studentData, null, 2));

    try {
        const response = await fetch(`${API_BASE}/api/students/add`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(studentData)
        });

        console.log(`\n📡 响应状态: ${response.status}`);

        const responseText = await response.text();
        console.log(`📡 响应内容: ${responseText}`);

        if (response.ok) {
            console.log('✅ 学生创建成功！修复有效！');
            try {
                const result = JSON.parse(responseText);
                return { success: true, data: result };
            } catch (e) {
                return { success: true, data: responseText };
            }
        } else {
            console.log(`❌ 学生创建仍然失败: ${response.status}`);
            return { success: false, error: responseText, status: response.status };
        }
    } catch (error) {
        console.log(`❌ 请求异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 测试多种birthday格式
async function testVariousBirthdayFormats(token) {
    console.log('\n🧪 测试各种birthday格式...');
    
    const testCases = [
        { name: "字符串时间戳", birthday: "*************" },
        { name: "数字时间戳", birthday: ************* },
        { name: "null值", birthday: null },
        { name: "空字符串", birthday: "" }
    ];

    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n📋 测试案例 ${i + 1}: ${testCase.name}`);
        
        const studentData = {
            "name": `测试学生-${testCase.name}`,
            "gender": "male",
            "phone": `1380013800${i}`,
            "idCard": `36011120140502207${i}`,
            "birthday": testCase.birthday,
            "source": "测试",
            "sourceDesc": "修复验证测试",
            "referrer": "测试",
            "intention": "A",
            "follower": "org-admin-id",
            "address": "测试地址",
            "remarks": "修复验证",
            "type": "formal"
        };

        try {
            const response = await fetch(`${API_BASE}/api/students/add`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(studentData)
            });

            if (response.ok) {
                console.log(`   ✅ ${testCase.name} - 创建成功`);
            } else {
                const error = await response.text();
                console.log(`   ❌ ${testCase.name} - 创建失败: ${response.status}`);
                console.log(`   错误详情: ${error}`);
            }
        } catch (error) {
            console.log(`   ❌ ${testCase.name} - 请求异常: ${error.message}`);
        }
    }
}

// 主测试函数
async function runFixVerification() {
    console.log('🔧 开始验证修复效果...\n');

    // 检查服务器状态
    const serverOk = await checkServer();
    if (!serverOk) {
        console.log('❌ 服务器未运行，请先启动服务器: npm run dev');
        return;
    }
    console.log('✅ 服务器运行正常\n');

    // 登录
    const token = await login();
    if (!token) {
        console.log('\n❌ 登录失败，无法继续测试');
        return;
    }

    // 测试原始错误数据
    const originalTestResult = await testStudentCreation(token);
    
    if (originalTestResult.success) {
        console.log('\n🎉 原始错误数据现在可以成功创建！');
        
        // 测试各种birthday格式
        await testVariousBirthdayFormats(token);
        
        console.log('\n📊 修复验证结果:');
        console.log('=' .repeat(50));
        console.log('✅ 修复成功！');
        console.log('   - 原始错误数据现在可以正常创建');
        console.log('   - birthday字段的BigInt转换问题已解决');
        console.log('   - 外键约束错误已修复');
        
    } else {
        console.log('\n❌ 修复可能不完整，原始错误数据仍然失败');
        console.log('   需要进一步调查其他可能的问题');
    }
}

// 执行验证
runFixVerification().catch(error => {
    console.error('验证执行失败:', error);
    process.exit(1);
});

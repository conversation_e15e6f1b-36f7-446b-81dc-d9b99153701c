import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 调试认证中间件脚本
 * 模拟认证中间件的行为，检查用户数据设置
 */

async function debugAuthMiddleware() {
    console.log('🔍 开始调试认证中间件...\n');

    try {
        // 模拟认证中间件的查询逻辑
        const userId = 'org-admin-id';
        
        console.log(`👤 检查用户: ${userId}`);
        
        // 执行与认证中间件相同的查询
        const userQuery = `
            SELECT
                u.id,
                u.account,
                u.name,
                ARRAY_AGG(DISTINCT r.id) AS role_ids,
                ARRAY_AGG(DISTINCT rp."permissionId") AS permission_ids,
                EXISTS (
                    SELECT 1
                    FROM user_roles ur
                    JOIN roles r ON ur."roleId" = r.id
                    WHERE ur."userId" = u.id AND r.code = 'SYSTEM_ADMIN'
                ) AS is_super_admin,
                (
                    SELECT ARRAY_AGG(DISTINCT ui."institutionId")
                    FROM user_institution ui
                    WHERE ui."userId" = u.id
                ) AS institution_ids
            FROM users u
            LEFT JOIN user_roles ur ON ur."userId" = u.id
            LEFT JOIN roles r ON ur."roleId" = r.id
            LEFT JOIN role_permissions rp ON rp."roleId" = r.id
            WHERE u.id = $1 AND u.active = true
            GROUP BY u.id
        `;

        const result = await prisma.$queryRawUnsafe(userQuery, userId);
        
        if (result.length === 0) {
            console.log('❌ 用户不存在或未激活');
            return;
        }

        const user = result[0];
        console.log('📋 用户查询结果:');
        console.log(`   ID: ${user.id}`);
        console.log(`   账号: ${user.account}`);
        console.log(`   姓名: ${user.name}`);
        console.log(`   角色IDs: ${JSON.stringify(user.role_ids)}`);
        console.log(`   权限IDs数量: ${user.permission_ids?.length || 0}`);
        console.log(`   是否超级管理员: ${user.is_super_admin}`);
        console.log(`   机构IDs: ${JSON.stringify(user.institution_ids)}`);

        // 获取权限代码
        if (user.permission_ids && user.permission_ids.length > 0) {
            const operationsQuery = `
                SELECT DISTINCT code
                FROM permissions
                WHERE id = ANY($1)
            `;

            const operationsResult = await prisma.$queryRawUnsafe(operationsQuery, user.permission_ids);
            console.log(`   权限代码数量: ${operationsResult.length}`);
            console.log(`   权限代码示例: ${operationsResult.slice(0, 5).map(op => op.code).join(', ')}...`);
        }

        // 构建认证中间件会设置的用户数据
        const userData = {
            id: user.id,
            account: user.account,
            SYSTEM_ADMIN: user.is_super_admin,
            institutionId: user.institution_ids?.[0] || null,
            operations: [], // 这里简化处理
            lastAuthenticated: new Date().toISOString()
        };

        console.log('\n🔧 认证中间件会设置的request.user:');
        console.log(JSON.stringify(userData, null, 2));

        // 检查机构关联详情
        if (user.institution_ids && user.institution_ids.length > 0) {
            console.log('\n🏢 机构关联详情:');
            
            for (const institutionId of user.institution_ids) {
                const institution = await prisma.institution.findUnique({
                    where: { id: institutionId },
                    select: {
                        id: true,
                        name: true,
                        userId: true
                    }
                });
                
                if (institution) {
                    console.log(`   - ${institution.name} (${institution.id})`);
                    console.log(`     管理员: ${institution.userId}`);
                } else {
                    console.log(`   - ❌ 机构 ${institutionId} 不存在`);
                }
            }
        } else {
            console.log('\n❌ 用户没有关联任何机构！');
        }

        // 验证学生创建所需的字段
        console.log('\n📝 学生创建验证:');
        console.log(`   request.user.id: ${userData.id} (用于operatorId)`);
        console.log(`   request.user.institutionId: ${userData.institutionId} (用于institutionId)`);
        
        if (!userData.institutionId) {
            console.log('   ❌ institutionId为null，这会导致外键约束失败！');
            console.log('   💡 需要确保用户有机构关联');
        } else {
            console.log('   ✅ institutionId有效');
        }

        // 检查follower字段
        const followerId = 'org-admin-id';
        const followerExists = await prisma.user.findUnique({
            where: { id: followerId },
            select: { id: true, name: true, account: true }
        });

        console.log(`\n👥 Follower验证:`);
        if (followerExists) {
            console.log(`   ✅ follower用户存在: ${followerExists.name} (${followerExists.account})`);
        } else {
            console.log(`   ❌ follower用户不存在: ${followerId}`);
        }

        console.log('\n✅ 认证中间件调试完成！');

    } catch (error) {
        console.error('❌ 调试过程中出现错误:', error);
        throw error;
    }
}

// 执行调试
debugAuthMiddleware().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
}).finally(async () => {
    await prisma.$disconnect();
});
